import 'product.dart';

class ShoppingCartItem {
  final int id;
  final int userId;
  final int productId;
  final int quantity;
  final double price;
  final Map<String, dynamic>? productOptions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Product? product;

  ShoppingCartItem({
    required this.id,
    required this.userId,
    required this.productId,
    required this.quantity,
    required this.price,
    this.productOptions,
    required this.createdAt,
    required this.updatedAt,
    this.product,
  });

  factory ShoppingCartItem.fromJson(Map<String, dynamic> json) {
    return ShoppingCartItem(
      id: json['id'],
      userId: json['user_id'],
      productId: json['product_id'],
      quantity: json['quantity'],
      price: double.parse(json['price'].toString()),
      productOptions: json['product_options'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      product: json['product'] != null ? Product.fromJson(json['product']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'product_id': productId,
      'quantity': quantity,
      'price': price,
      'product_options': productOptions,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'product': product?.toJson(),
    };
  }

  double get totalPrice => quantity * price;
  
  String get formattedTotalPrice => '\$${totalPrice.toStringAsFixed(2)}';
  
  String get formattedUnitPrice => '\$${price.toStringAsFixed(2)}';
}

class ShoppingCart {
  final List<ShoppingCartItem> items;
  final double total;
  final int count;

  ShoppingCart({
    required this.items,
    required this.total,
    required this.count,
  });

  factory ShoppingCart.fromJson(Map<String, dynamic> json) {
    final items = json['cart_items'] != null 
        ? (json['cart_items'] as List).map((item) => ShoppingCartItem.fromJson(item)).toList()
        : <ShoppingCartItem>[];
    
    return ShoppingCart(
      items: items,
      total: double.parse(json['cart_total']?.toString() ?? '0'),
      count: json['cart_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cart_items': items.map((item) => item.toJson()).toList(),
      'cart_total': total,
      'cart_count': count,
    };
  }

  String get formattedTotal => '\$${total.toStringAsFixed(2)}';
  
  bool get isEmpty => items.isEmpty;
  
  bool get isNotEmpty => items.isNotEmpty;
  
  double get subtotal => items.fold(0, (sum, item) => sum + item.totalPrice);
  
  bool get hasPhysicalItems => items.any((item) => item.product?.isPhysical == true);
  
  bool get hasDigitalItems => items.any((item) => item.product?.isDigital == true);
}
