import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/product.dart';
import '../models/shopping_cart.dart';
import '../models/order.dart';
import '../utils/constants.dart';

class EcommerceService {
  static const String baseUrl = Constants.baseUrl;

  // Get authentication headers
  static Map<String, String> _getHeaders({String? token}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    return headers;
  }

  // Products
  static Future<List<Product>> getProducts({
    String? category,
    String? search,
    String? sort,
    String? type,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;
      if (sort != null) queryParams['sort'] = sort;
      if (type != null) queryParams['type'] = type;
      if (minPrice != null) queryParams['min_price'] = minPrice.toString();
      if (maxPrice != null) queryParams['max_price'] = maxPrice.toString();

      final uri = Uri.parse('$baseUrl/api/shop/products').replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders());

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final productsData = data['products']['data'] ?? data['products'] ?? [];
        return (productsData as List).map((product) => Product.fromJson(product)).toList();
      } else {
        throw Exception('Failed to load products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading products: $e');
    }
  }

  static Future<Product> getProduct(String slug) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/products/$slug'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Product.fromJson(data['product']);
      } else {
        throw Exception('Failed to load product: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading product: $e');
    }
  }

  static Future<List<ProductCategory>> getCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/categories'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final categoriesData = data['categories'] ?? [];
        return (categoriesData as List).map((category) => ProductCategory.fromJson(category)).toList();
      } else {
        throw Exception('Failed to load categories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading categories: $e');
    }
  }

  static Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/featured-products'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final productsData = data['products'] ?? [];
        return (productsData as List).map((product) => Product.fromJson(product)).toList();
      } else {
        throw Exception('Failed to load featured products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading featured products: $e');
    }
  }

  static Future<List<Product>> searchProducts(String query, {int limit = 10}) async {
    try {
      final uri = Uri.parse('$baseUrl/api/shop/search-products').replace(queryParameters: {
        'q': query,
        'limit': limit.toString(),
      });
      
      final response = await http.get(uri, headers: _getHeaders());

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final productsData = data['products'] ?? [];
        return (productsData as List).map((product) => Product.fromJson(product)).toList();
      } else {
        throw Exception('Failed to search products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error searching products: $e');
    }
  }

  // Shopping Cart
  static Future<ShoppingCart> getCart(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/cart'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return ShoppingCart.fromJson(data);
      } else {
        throw Exception('Failed to load cart: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading cart: $e');
    }
  }

  static Future<Map<String, dynamic>> addToCart(String token, int productId, {int quantity = 1, Map<String, dynamic>? options}) async {
    try {
      final body = {
        'product_id': productId,
        'quantity': quantity,
        if (options != null) 'options': options,
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/cart/add'),
        headers: _getHeaders(token: token),
        body: json.encode(body),
      );

      final data = json.decode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to add to cart');
      }
    } catch (e) {
      throw Exception('Error adding to cart: $e');
    }
  }

  static Future<Map<String, dynamic>> updateCartItem(String token, int productId, int quantity) async {
    try {
      final body = {'quantity': quantity};

      final response = await http.put(
        Uri.parse('$baseUrl/api/shop/cart/$productId'),
        headers: _getHeaders(token: token),
        body: json.encode(body),
      );

      final data = json.decode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to update cart');
      }
    } catch (e) {
      throw Exception('Error updating cart: $e');
    }
  }

  static Future<Map<String, dynamic>> removeFromCart(String token, int productId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/shop/cart/$productId'),
        headers: _getHeaders(token: token),
      );

      final data = json.decode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to remove from cart');
      }
    } catch (e) {
      throw Exception('Error removing from cart: $e');
    }
  }

  static Future<Map<String, dynamic>> clearCart(String token) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/shop/cart'),
        headers: _getHeaders(token: token),
      );

      final data = json.decode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Failed to clear cart');
      }
    } catch (e) {
      throw Exception('Error clearing cart: $e');
    }
  }

  static Future<int> getCartCount(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/cart/count'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['cart_count'] ?? 0;
      } else {
        return 0;
      }
    } catch (e) {
      return 0;
    }
  }

  // Orders
  static Future<List<Order>> getOrders(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/orders'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final ordersData = data['orders']['data'] ?? data['orders'] ?? [];
        return (ordersData as List).map((order) => Order.fromJson(order)).toList();
      } else {
        throw Exception('Failed to load orders: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading orders: $e');
    }
  }

  static Future<Order> getOrder(String token, String orderNumber) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/orders/$orderNumber'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Order.fromJson(data['order']);
      } else {
        throw Exception('Failed to load order: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading order: $e');
    }
  }

  static Future<Map<String, dynamic>> checkout(String token, Map<String, dynamic> checkoutData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/checkout'),
        headers: _getHeaders(token: token),
        body: json.encode(checkoutData),
      );

      final data = json.decode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Checkout failed');
      }
    } catch (e) {
      throw Exception('Error during checkout: $e');
    }
  }

  static Future<Map<String, dynamic>> confirmPayment(String token, String orderNumber, String paymentIntentId) async {
    try {
      final body = {'payment_intent_id': paymentIntentId};

      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/orders/$orderNumber/confirm-payment'),
        headers: _getHeaders(token: token),
        body: json.encode(body),
      );

      final data = json.decode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Payment confirmation failed');
      }
    } catch (e) {
      throw Exception('Error confirming payment: $e');
    }
  }
}
