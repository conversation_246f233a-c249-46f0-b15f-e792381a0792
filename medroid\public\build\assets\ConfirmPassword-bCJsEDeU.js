import{N as l,V as f,p as i,e as m,g as t,f as a,i as r,u as s,m as c,v as u,q as n,j as _}from"./vendor-to-baZ3O.js";import{_ as w}from"./InputError.vue_vue_type_script_setup_true_lang-BFs6BInq.js";import{_ as C}from"./index-mwQ5fdQw.js";import{_ as g,a as V}from"./Label.vue_vue_type_script_setup_true_lang-Cr4GDSuL.js";import{L as b,_ as h}from"./AuthLayout.vue_vue_type_script_setup_true_lang-BVdP_X_R.js";import"./Primitive-8XtBwUMh.js";import"./index-7ISyTS5f.js";import"./createLucideIcon-FE_-qnYD.js";const v={class:"space-y-6"},x={class:"grid gap-2"},y={class:"flex items-center"},M=l({__name:"ConfirmPassword",setup(N){const o=f({password:""}),d=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset()}})};return($,e)=>(m(),i(h,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing."},{default:t(()=>[a(s(c),{title:"Confirm password"}),r("form",{onSubmit:u(d,["prevent"])},[r("div",v,[r("div",x,[a(s(g),{htmlFor:"password"},{default:t(()=>e[1]||(e[1]=[n("Password")])),_:1}),a(s(V),{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s(o).password,"onUpdate:modelValue":e[0]||(e[0]=p=>s(o).password=p),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(w,{message:s(o).errors.password},null,8,["message"])]),r("div",y,[a(s(C),{class:"w-full",disabled:s(o).processing},{default:t(()=>[s(o).processing?(m(),i(s(b),{key:0,class:"h-4 w-4 animate-spin"})):_("",!0),e[2]||(e[2]=n(" Confirm Password "))]),_:1},8,["disabled"])])])],32)]),_:1}))}});export{M as default};
