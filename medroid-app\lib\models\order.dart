import 'product.dart';

class Order {
  final int id;
  final String orderNumber;
  final int userId;
  final String status;
  final double subtotal;
  final double taxAmount;
  final double shippingAmount;
  final double discountAmount;
  final double totalAmount;
  final String currency;
  final String paymentStatus;
  final String? paymentMethod;
  final String? paymentIntentId;
  final Map<String, dynamic> billingAddress;
  final Map<String, dynamic>? shippingAddress;
  final String? shippingMethod;
  final String? trackingNumber;
  final DateTime? shippedAt;
  final DateTime? deliveredAt;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<OrderItem>? items;

  Order({
    required this.id,
    required this.orderNumber,
    required this.userId,
    required this.status,
    required this.subtotal,
    required this.taxAmount,
    required this.shippingAmount,
    required this.discountAmount,
    required this.totalAmount,
    required this.currency,
    required this.paymentStatus,
    this.paymentMethod,
    this.paymentIntentId,
    required this.billingAddress,
    this.shippingAddress,
    this.shippingMethod,
    this.trackingNumber,
    this.shippedAt,
    this.deliveredAt,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.items,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      orderNumber: json['order_number'],
      userId: json['user_id'],
      status: json['status'],
      subtotal: double.parse(json['subtotal'].toString()),
      taxAmount: double.parse(json['tax_amount'].toString()),
      shippingAmount: double.parse(json['shipping_amount'].toString()),
      discountAmount: double.parse(json['discount_amount'].toString()),
      totalAmount: double.parse(json['total_amount'].toString()),
      currency: json['currency'],
      paymentStatus: json['payment_status'],
      paymentMethod: json['payment_method'],
      paymentIntentId: json['payment_intent_id'],
      billingAddress: json['billing_address'],
      shippingAddress: json['shipping_address'],
      shippingMethod: json['shipping_method'],
      trackingNumber: json['tracking_number'],
      shippedAt: json['shipped_at'] != null ? DateTime.parse(json['shipped_at']) : null,
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      items: json['items'] != null ? (json['items'] as List).map((item) => OrderItem.fromJson(item)).toList() : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_number': orderNumber,
      'user_id': userId,
      'status': status,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'shipping_amount': shippingAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'currency': currency,
      'payment_status': paymentStatus,
      'payment_method': paymentMethod,
      'payment_intent_id': paymentIntentId,
      'billing_address': billingAddress,
      'shipping_address': shippingAddress,
      'shipping_method': shippingMethod,
      'tracking_number': trackingNumber,
      'shipped_at': shippedAt?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'items': items?.map((item) => item.toJson()).toList(),
    };
  }

  String get formattedTotal => '\$${totalAmount.toStringAsFixed(2)}';
  String get formattedSubtotal => '\$${subtotal.toStringAsFixed(2)}';
  String get formattedShipping => '\$${shippingAmount.toStringAsFixed(2)}';
  String get formattedTax => '\$${taxAmount.toStringAsFixed(2)}';

  String get statusLabel {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      case 'refunded':
        return 'Refunded';
      default:
        return status.toUpperCase();
    }
  }

  String get paymentStatusLabel {
    switch (paymentStatus) {
      case 'pending':
        return 'Pending';
      case 'paid':
        return 'Paid';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return paymentStatus.toUpperCase();
    }
  }

  bool get hasPhysicalItems => items?.any((item) => item.productType == 'physical') ?? false;
  bool get hasDigitalItems => items?.any((item) => item.productType == 'digital') ?? false;
  bool get canBeCancelled => ['pending', 'processing'].contains(status);
  bool get canBeShipped => status == 'processing' && hasPhysicalItems;
}

class OrderItem {
  final int id;
  final int orderId;
  final int productId;
  final String productName;
  final String productSku;
  final String productType;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final Map<String, dynamic>? productOptions;
  final List<Map<String, dynamic>>? digitalFiles;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Product? product;

  OrderItem({
    required this.id,
    required this.orderId,
    required this.productId,
    required this.productName,
    required this.productSku,
    required this.productType,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.productOptions,
    this.digitalFiles,
    required this.createdAt,
    required this.updatedAt,
    this.product,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      id: json['id'],
      orderId: json['order_id'],
      productId: json['product_id'],
      productName: json['product_name'],
      productSku: json['product_sku'],
      productType: json['product_type'],
      quantity: json['quantity'],
      unitPrice: double.parse(json['unit_price'].toString()),
      totalPrice: double.parse(json['total_price'].toString()),
      productOptions: json['product_options'],
      digitalFiles: json['digital_files'] != null ? List<Map<String, dynamic>>.from(json['digital_files']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      product: json['product'] != null ? Product.fromJson(json['product']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'product_id': productId,
      'product_name': productName,
      'product_sku': productSku,
      'product_type': productType,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'product_options': productOptions,
      'digital_files': digitalFiles,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'product': product?.toJson(),
    };
  }

  String get formattedUnitPrice => '\$${unitPrice.toStringAsFixed(2)}';
  String get formattedTotalPrice => '\$${totalPrice.toStringAsFixed(2)}';
  bool get isDigital => productType == 'digital';
  bool get isPhysical => productType == 'physical';
}
