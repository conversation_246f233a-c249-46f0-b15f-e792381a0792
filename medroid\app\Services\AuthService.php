<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class AuthService
{
    /**
     * Authenticate a user and return a token with role information
     *
     * @param string $email
     * @param string $password
     * @param string $deviceName
     * @return array|null
     */
    public function authenticate($email, $password, $deviceName = 'mobile')
    {
        $user = User::where('email', $email)->first();

        if (!$user || !Hash::check($password, $user->password)) {
            return null;
        }

        // Load role-specific data
        $this->loadUserRoleData($user);

        // Create token with role abilities (no expiration for better Flutter compatibility)
        $abilities = $this->getRoleAbilities($user->role);
        $tokenResult = $user->createToken($deviceName, $abilities);
        $token = $tokenResult->plainTextToken;

        // Don't set expiration - let tokens persist for better mobile app experience

        return [
            'user' => $user,
            'token' => $token,
        ];
    }

    /**
     * Load role-specific relationships
     *
     * @param User $user
     * @return void
     */
    public function loadUserRoleData(User $user)
    {
        $user->load('roles');
        
        if ($user->role === 'patient') {
            $user->load('patient');
        } else if ($user->role === 'provider') {
            $user->load('provider');
        }
    }

    /**
     * Get abilities for a specific role
     *
     * @param string $role
     * @return array
     */
    protected function getRoleAbilities($role)
    {
        return match ($role) {
            'admin' => ['*'],
            'provider' => ['provider:access', 'appointments:manage'],
            'patient' => ['patient:access', 'appointments:view'],
            default => ['basic:access'],
        };
    }
}