import{N as p,c as l,p as v,e as n,g as m,Q as y,S as x,u as s,a7 as w,a8 as z,d as h,i as r,j as B,t as g,H as $,f,F as C,l as S,P as N,q as b,n as k}from"./vendor-to-baZ3O.js";import{_ as I}from"./index-mwQ5fdQw.js";import{P as O,c as V}from"./Primitive-8XtBwUMh.js";import{r as A}from"./index-7ISyTS5f.js";const E=p({__name:"BaseSeparator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o,a=["horizontal","vertical"];function i(t){return a.includes(t)}const c=l(()=>i(e.orientation)?e.orientation:"horizontal"),_=l(()=>c.value==="vertical"?e.orientation:void 0),d=l(()=>e.decorative?{role:"none"}:{"aria-orientation":_.value,role:"separator"});return(t,P)=>(n(),v(s(O),x({as:t.as,"as-child":t.asChild,"data-orientation":c.value},d.value),{default:m(()=>[y(t.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),L=p({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean},asChild:{type:Boolean},as:{}},setup(o){const e=o;return(a,i)=>(n(),v(E,w(z(e)),{default:m(()=>[y(a.$slots,"default")]),_:3},16))}}),R={class:"mb-8 space-y-0.5"},T={class:"text-xl font-semibold tracking-tight"},j={key:0,class:"text-sm text-muted-foreground"},F=p({__name:"Heading",props:{title:{},description:{}},setup(o){return(e,a)=>(n(),h("div",R,[r("h2",T,g(e.title),1),e.description?(n(),h("p",j,g(e.description),1)):B("",!0)]))}}),H=p({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(o){const e=o,a=A(e,"class");return(i,c)=>(n(),v(s(L),x({"data-slot":"separator-root"},s(a),{class:s(V)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e.class)}),null,16,["class"]))}}),M={class:"px-4 py-6"},U={class:"flex flex-col space-y-8 md:space-y-0 lg:flex-row lg:space-x-12 lg:space-y-0"},q={class:"w-full max-w-xl lg:w-48"},D={class:"flex flex-col space-x-0 space-y-1"},Q={class:"flex-1 md:max-w-2xl"},G={class:"max-w-xl space-y-12"},Y=p({__name:"Layout",setup(o){var d;const e=$(),a=l(()=>{var t;return(t=e.props.auth)==null?void 0:t.user}),i=l(()=>{var t;return((t=a.value)==null?void 0:t.role)==="patient"}),c=[{title:"Profile",href:"/settings/profile"},{title:"Password",href:"/settings/password"},{title:"Appearance",href:"/settings/appearance"},...i.value?[{title:"Appointment Preferences",href:"/settings/appointment-preferences"},{title:"Medical Information",href:"/settings/medical-info"},{title:"Emergency Contacts",href:"/settings/emergency-contacts"},{title:"Insurance Information",href:"/settings/insurance"},{title:"Communication Preferences",href:"/settings/communication"},{title:"Privacy & Security",href:"/settings/privacy"}]:[]],_=(d=e.props.ziggy)!=null&&d.location?new URL(e.props.ziggy.location).pathname:"";return(t,P)=>(n(),h("div",M,[f(F,{title:"Settings",description:"Manage your profile and account settings"}),r("div",U,[r("aside",q,[r("nav",D,[(n(),h(C,null,S(c,u=>f(s(I),{key:u.href,variant:"ghost",class:k(["w-full justify-start",{"bg-muted":s(_)===u.href}]),"as-child":""},{default:m(()=>[f(s(N),{href:u.href},{default:m(()=>[b(g(u.title),1)]),_:2},1032,["href"])]),_:2},1032,["class"])),64))])]),f(s(H),{class:"my-6 md:hidden"}),r("div",Q,[r("section",G,[y(t.$slots,"default")])])])]))}});export{Y as _,F as a,H as b};
