<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        "name",
        "email",
        "password",
        "role",
        "signup_source",
        "phone_number",
        "date_of_birth",
        "is_active",
        "email_verified_at",
        "profile_image",
        "referral_code",
        "referred_by",
        "is_founder_member",
        "total_points",
        "activity_streak",
        "last_activity_at",
        "last_login_at",
        "club_memberships",
        "sso_provider",
        "sso_provider_id",
        "sso_access_token",
        "sso_refresh_token",
        "sso_token_expires_at",
        "sso_profile_data",
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        "password",
        "remember_token",
        "sso_access_token",
        "sso_refresh_token",
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            "email_verified_at" => "datetime",
            "date_of_birth" => "date",
            "is_active" => "boolean",
            "is_founder_member" => "boolean",
            "last_activity_at" => "datetime",
            "last_login_at" => "datetime",
            "club_memberships" => "array",
            "sso_token_expires_at" => "datetime",
            "sso_profile_data" => "array",
            'password' => 'hashed',
        ];
    }

    /**
     * Ensure role is never null - always return 'patient' as default
     */
    public function getRoleAttribute($value)
    {
        return $value ?? 'patient';
    }

    /**
     * Ensure phone_number is never null - return empty string as default
     */
    public function getPhoneNumberAttribute($value)
    {
        return $value ?? '';
    }

    /**
     * Ensure profile_image is never null - return empty string as default
     */
    public function getProfileImageAttribute($value)
    {
        return $value ?? '';
    }

    /**
     * Ensure referral_code is never null - return empty string as default
     */
    public function getReferralCodeAttribute($value)
    {
        return $value ?? '';
    }

    /**
     * Override toArray to ensure no null string values in API responses
     */
    public function toArray()
    {
        $array = parent::toArray();

        // Ensure string fields are never null
        $stringFields = ['phone_number', 'profile_image', 'referral_code', 'role'];
        foreach ($stringFields as $field) {
            if (array_key_exists($field, $array) && is_null($array[$field])) {
                $array[$field] = '';
            }
        }

        return $array;
    }

    /**
     * Get the patient profile associated with the user.
     */
    public function patient()
    {
        return $this->hasOne(Patient::class);
    }

    /**
     * Get the provider profile associated with the user.
     */
    public function provider()
    {
        return $this->hasOne(Provider::class);
    }

    /**
     * Get the clinics associated with the user through provider or patient relationships.
     */
    public function clinics()
    {
        $clinics = collect();

        // Get clinic through provider relationship
        if ($this->provider && $this->provider->clinic) {
            $clinics->push($this->provider->clinic);
        }

        // Get clinic through patient relationship
        if ($this->patient && $this->patient->clinic) {
            $clinics->push($this->patient->clinic);
        }

        return $clinics->unique('id');
    }

    /**
     * Get the primary clinic for the user (first clinic found).
     */
    public function primaryClinic()
    {
        return $this->clinics()->first();
    }

    /**
     * Get the user who referred this user.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    /**
     * Get all users referred by this user.
     */
    public function referrals()
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    /**
     * Get all referral records where this user is the referrer.
     */
    public function referralRecords()
    {
        return $this->hasMany(Referral::class, 'referrer_id');
    }

    /**
     * Get the referral record where this user was referred.
     */
    public function referredRecord()
    {
        return $this->hasOne(Referral::class, 'referred_id');
    }

    /**
     * Get the user's access tokens.
     */
    public function tokens()
    {
        return $this->hasMany(PersonalAccessToken::class, 'user_id');
    }

    /**
     * Get the user's credit balance.
     */
    public function credit()
    {
        return $this->hasOne(UserCredit::class);
    }

    /**
     * Get the user's club memberships.
     */
    public function clubMemberships()
    {
        return $this->hasMany(UserClub::class);
    }

    /**
     * Alias for clubMemberships for consistency
     */
    public function clubs()
    {
        return $this->clubMemberships();
    }

    /**
     * Get the user's founder club membership.
     */
    public function founderMembership()
    {
        return $this->hasOne(UserClub::class)->where('club_type', 'founder');
    }

    /**
     * Get the user's badges.
     */
    public function badges()
    {
        return $this->hasMany(UserBadge::class);
    }

    /**
     * Check if user is a founder member.
     */
    public function isFounderMember()
    {
        return $this->is_founder_member || $this->founderMembership()->exists();
    }

    /**
     * Get founder club display information.
     */
    public function getFounderClubInfo()
    {
        $membership = $this->founderMembership;
        if (!$membership) {
            return null;
        }

        return [
            'club_name' => 'Medroid Founders Club',
            'membership_level' => ucfirst($membership->membership_level),
            'founder_code_used' => $membership->founder_code_used,
            'joined_at' => $membership->joined_at,
            'is_verified' => $membership->is_verified,
        ];
    }
}
