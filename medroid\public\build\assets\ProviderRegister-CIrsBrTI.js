import{N as _,s as O,r as f,d as a,e as d,f as l,i as o,u as R,m as F,v as T,x as i,y as c,q as m,K as v,O as B,z as D,F as x,l as N,j as $,t as y,a as I}from"./vendor-to-baZ3O.js";import{_ as u}from"./InputError.vue_vue_type_script_setup_true_lang-BFs6BInq.js";const G={class:"min-h-screen bg-medroid-sage py-12"},H={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"},J={class:"bg-white rounded-lg shadow-lg p-8"},K={class:"border-b border-gray-200 pb-6"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={class:"flex space-x-4"},W={class:"flex items-center"},X={class:"flex items-center"},Z={class:"flex items-center"},ee={class:"border-b border-gray-200 pb-6"},oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},se={class:"relative"},re=["type"],te={key:0,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ne={key:1,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},le={class:"relative"},ie=["type"],ae={key:0,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},de={key:1,class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ue={class:"border-b border-gray-200 pb-6"},me={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},pe=["value"],ce={class:"mt-6"},ge={class:"mt-6"},be={class:"grid grid-cols-2 md:grid-cols-3 gap-3"},fe=["value","checked","onChange"],ve={class:"pt-6"},xe=["disabled"],ye={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Ce=_({__name:"ProviderRegister",props:{specializations:{type:Array,default:()=>[]},languages:{type:Array,default:()=>[]}},setup(w){const s=O({name:"",email:"",password:"",password_confirmation:"",phone:"",gender:"",specialization:"",license_number:"",bio:"",languages:[]}),n=f({}),g=f(!1),b=f(!1),p=f(!1),S=async()=>{p.value=!0,n.value={};try{console.log("Submitting form data:",s);const t=await I.post("/api/provider/register",s,{headers:{"Content-Type":"application/json",Accept:"application/json"}});console.log("Response:",t.data),t.data.success&&(alert("Registration submitted successfully! You will receive an email confirmation within 24 hours."),Object.keys(s).forEach(e=>{e==="languages"?s[e]=[]:s[e]=""}))}catch(t){if(console.error("Full error object:",t),t.response)if(console.error("Error response:",t.response.data),console.error("Error status:",t.response.status),t.response.status===422){n.value=t.response.data.errors||{},console.error("Validation errors:",n.value),console.error("Debug info:",t.response.data.debug);const e=Object.values(n.value).flat();e.length>0&&alert(`Validation errors:
`+e.join(`
`))}else alert(`Error ${t.response.status}: ${t.response.data.message||"An error occurred during registration."}`);else t.request?(console.error("No response received:",t.request),alert("No response from server. Please check your connection and try again.")):(console.error("Request setup error:",t.message),alert("An error occurred setting up the request. Please try again."))}finally{p.value=!1}},U=()=>{g.value=!g.value},E=()=>{b.value=!b.value},L=t=>{const e=s.languages.indexOf(t);e>-1?s.languages.splice(e,1):s.languages.push(t)};return(t,e)=>{var k,h,C,M,V,z,P,j,q,A;return d(),a(x,null,[l(R(F),{title:"Provider Registration"}),o("div",G,[o("div",H,[e[35]||(e[35]=o("div",{class:"text-center mb-8"},[o("h1",{class:"text-4xl font-bold text-medroid-navy mb-4"}," Join Our Provider Network "),o("p",{class:"text-lg text-medroid-slate max-w-2xl mx-auto"}," Become part of Medroid's healthcare network and help provide quality care to patients worldwide. ")],-1)),o("div",J,[o("form",{onSubmit:T(S,["prevent"]),class:"space-y-6"},[o("div",K,[e[18]||(e[18]=o("h2",{class:"text-xl font-semibold text-medroid-navy mb-4"},"Personal Information",-1)),o("div",Y,[o("div",null,[e[11]||(e[11]=o("label",{for:"name",class:"block text-sm font-medium text-medroid-navy mb-2"},"Full Name *",-1)),i(o("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=r=>s.name=r),type:"text",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your full name"},null,512),[[c,s.name]]),l(u,{class:"mt-2",message:(k=n.value.name)==null?void 0:k[0]},null,8,["message"])]),o("div",null,[e[12]||(e[12]=o("label",{for:"email",class:"block text-sm font-medium text-medroid-navy mb-2"},"Email Address *",-1)),i(o("input",{id:"email","onUpdate:modelValue":e[1]||(e[1]=r=>s.email=r),type:"email",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your email address"},null,512),[[c,s.email]]),l(u,{class:"mt-2",message:(h=n.value.email)==null?void 0:h[0]},null,8,["message"])]),o("div",null,[e[13]||(e[13]=o("label",{for:"phone",class:"block text-sm font-medium text-medroid-navy mb-2"},"Phone Number *",-1)),i(o("input",{id:"phone","onUpdate:modelValue":e[2]||(e[2]=r=>s.phone=r),type:"tel",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your phone number"},null,512),[[c,s.phone]]),l(u,{class:"mt-2",message:(C=n.value.phone)==null?void 0:C[0]},null,8,["message"])]),o("div",null,[e[17]||(e[17]=o("label",{class:"block text-sm font-medium text-medroid-navy mb-2"},"Gender *",-1)),o("div",Q,[o("label",W,[i(o("input",{"onUpdate:modelValue":e[3]||(e[3]=r=>s.gender=r),type:"radio",value:"male",class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,512),[[v,s.gender]]),e[14]||(e[14]=m(" Male "))]),o("label",X,[i(o("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>s.gender=r),type:"radio",value:"female",class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,512),[[v,s.gender]]),e[15]||(e[15]=m(" Female "))]),o("label",Z,[i(o("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>s.gender=r),type:"radio",value:"other",class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,512),[[v,s.gender]]),e[16]||(e[16]=m(" Other "))])]),l(u,{class:"mt-2",message:(M=n.value.gender)==null?void 0:M[0]},null,8,["message"])])])]),o("div",ee,[e[25]||(e[25]=o("h2",{class:"text-xl font-semibold text-medroid-navy mb-4"},"Account Security",-1)),o("div",oe,[o("div",null,[e[21]||(e[21]=o("label",{for:"password",class:"block text-sm font-medium text-medroid-navy mb-2"},"Password *",-1)),o("div",se,[i(o("input",{id:"password","onUpdate:modelValue":e[6]||(e[6]=r=>s.password=r),type:g.value?"text":"password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Create a strong password"},null,8,re),[[B,s.password]]),o("button",{type:"button",onClick:U,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[g.value?(d(),a("svg",ne,e[20]||(e[20]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)]))):(d(),a("svg",te,e[19]||(e[19]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))])]),l(u,{class:"mt-2",message:(V=n.value.password)==null?void 0:V[0]},null,8,["message"])]),o("div",null,[e[24]||(e[24]=o("label",{for:"password_confirmation",class:"block text-sm font-medium text-medroid-navy mb-2"},"Confirm Password *",-1)),o("div",le,[i(o("input",{id:"password_confirmation","onUpdate:modelValue":e[7]||(e[7]=r=>s.password_confirmation=r),type:b.value?"text":"password",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Confirm your password"},null,8,ie),[[B,s.password_confirmation]]),o("button",{type:"button",onClick:E,class:"absolute inset-y-0 right-0 pr-4 flex items-center hover:text-medroid-orange transition-colors duration-200"},[b.value?(d(),a("svg",de,e[23]||(e[23]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"},null,-1)]))):(d(),a("svg",ae,e[22]||(e[22]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"},null,-1)])))])]),l(u,{class:"mt-2",message:(z=n.value.password_confirmation)==null?void 0:z[0]},null,8,["message"])])])]),o("div",ue,[e[32]||(e[32]=o("h2",{class:"text-xl font-semibold text-medroid-navy mb-4"},"Professional Information",-1)),o("div",me,[o("div",null,[e[27]||(e[27]=o("label",{for:"specialization",class:"block text-sm font-medium text-medroid-navy mb-2"},"Specialization *",-1)),i(o("select",{id:"specialization","onUpdate:modelValue":e[8]||(e[8]=r=>s.specialization=r),required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200"},[e[26]||(e[26]=o("option",{value:""},"Select your specialization",-1)),(d(!0),a(x,null,N(w.specializations,r=>(d(),a("option",{key:r,value:r},y(r),9,pe))),128))],512),[[D,s.specialization]]),l(u,{class:"mt-2",message:(P=n.value.specialization)==null?void 0:P[0]},null,8,["message"])]),o("div",null,[e[28]||(e[28]=o("label",{for:"license_number",class:"block text-sm font-medium text-medroid-navy mb-2"},"License Number *",-1)),i(o("input",{id:"license_number","onUpdate:modelValue":e[9]||(e[9]=r=>s.license_number=r),type:"text",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Enter your medical license number"},null,512),[[c,s.license_number]]),l(u,{class:"mt-2",message:(j=n.value.license_number)==null?void 0:j[0]},null,8,["message"])])]),o("div",ce,[e[29]||(e[29]=o("label",{for:"bio",class:"block text-sm font-medium text-medroid-navy mb-2"},"Professional Bio *",-1)),i(o("textarea",{id:"bio","onUpdate:modelValue":e[10]||(e[10]=r=>s.bio=r),rows:"4",required:"",class:"w-full px-4 py-3 border border-medroid-border rounded-lg focus:outline-none focus:ring-2 focus:ring-medroid-orange focus:border-medroid-orange transition-all duration-200",placeholder:"Tell us about your professional background, experience, and approach to patient care (minimum 50 characters)"},null,512),[[c,s.bio]]),e[30]||(e[30]=o("p",{class:"mt-1 text-sm text-medroid-slate"},"Minimum 50 characters, maximum 1000 characters",-1)),l(u,{class:"mt-2",message:(q=n.value.bio)==null?void 0:q[0]},null,8,["message"])]),o("div",ge,[e[31]||(e[31]=o("label",{class:"block text-sm font-medium text-medroid-navy mb-2"},"Languages Spoken",-1)),o("div",be,[(d(!0),a(x,null,N(w.languages,r=>(d(),a("label",{key:r,class:"flex items-center"},[o("input",{type:"checkbox",value:r,checked:s.languages.includes(r),onChange:we=>L(r),class:"mr-2 text-medroid-orange focus:ring-medroid-orange"},null,40,fe),m(" "+y(r),1)]))),128))]),l(u,{class:"mt-2",message:(A=n.value.languages)==null?void 0:A[0]},null,8,["message"])])]),o("div",ve,[o("button",{type:"submit",disabled:p.value,class:"w-full bg-medroid-orange hover:bg-medroid-orange/90 disabled:bg-medroid-orange/50 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center"},[p.value?(d(),a("svg",ye,e[33]||(e[33]=[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):$("",!0),m(" "+y(p.value?"Submitting Application...":"Submit Application"),1)],8,xe)]),e[34]||(e[34]=o("div",{class:"text-center pt-4"},[o("p",{class:"text-sm text-medroid-slate"},[m(" By submitting this application, you agree to our "),o("a",{href:"/terms-and-conditions",target:"_blank",class:"text-medroid-orange hover:text-medroid-orange/80 underline"},"Terms of Service"),m(" and "),o("a",{href:"/privacy-policy",target:"_blank",class:"text-medroid-orange hover:text-medroid-orange/80 underline"},"Privacy Policy")])],-1))],32)])])])],64)}}});export{Ce as default};
