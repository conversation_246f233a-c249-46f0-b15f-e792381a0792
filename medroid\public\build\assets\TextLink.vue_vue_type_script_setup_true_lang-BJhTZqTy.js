import{N as r,p as a,e as o,u as t,P as n,g as s,Q as d}from"./vendor-to-baZ3O.js";const l=r({__name:"TextLink",props:{href:{},tabindex:{},method:{},as:{}},setup(i){return(e,u)=>(o(),a(t(n),{href:e.href,tabindex:e.tabindex,method:e.method,as:e.as,class:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500"},{default:s(()=>[d(e.$slots,"default")]),_:3},8,["href","tabindex","method","as"]))}});export{l as _};
