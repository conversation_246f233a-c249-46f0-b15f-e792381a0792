class Product {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String? shortDescription;
  final String type; // 'physical' or 'digital'
  final int categoryId;
  final double price;
  final double? salePrice;
  final String sku;
  final int stockQuantity;
  final bool manageStock;
  final bool inStock;
  final double? weight;
  final String? dimensions;
  final Map<String, dynamic>? attributes;
  final String? featuredImage;
  final List<String>? galleryImages;
  final bool isFeatured;
  final bool isActive;
  final int sortOrder;
  final List<Map<String, dynamic>>? digitalFiles;
  final int? downloadLimit;
  final int? downloadExpiryDays;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ProductCategory? category;
  final List<ProductImage>? images;

  Product({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    this.shortDescription,
    required this.type,
    required this.categoryId,
    required this.price,
    this.salePrice,
    required this.sku,
    required this.stockQuantity,
    required this.manageStock,
    required this.inStock,
    this.weight,
    this.dimensions,
    this.attributes,
    this.featuredImage,
    this.galleryImages,
    required this.isFeatured,
    required this.isActive,
    required this.sortOrder,
    this.digitalFiles,
    this.downloadLimit,
    this.downloadExpiryDays,
    required this.createdAt,
    required this.updatedAt,
    this.category,
    this.images,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      shortDescription: json['short_description'],
      type: json['type'],
      categoryId: json['category_id'],
      price: double.parse(json['price'].toString()),
      salePrice: json['sale_price'] != null ? double.parse(json['sale_price'].toString()) : null,
      sku: json['sku'],
      stockQuantity: json['stock_quantity'],
      manageStock: json['manage_stock'] == 1 || json['manage_stock'] == true,
      inStock: json['in_stock'] == 1 || json['in_stock'] == true,
      weight: json['weight'] != null ? double.parse(json['weight'].toString()) : null,
      dimensions: json['dimensions'],
      attributes: json['attributes'],
      featuredImage: json['featured_image'],
      galleryImages: json['gallery_images'] != null ? List<String>.from(json['gallery_images']) : null,
      isFeatured: json['is_featured'] == 1 || json['is_featured'] == true,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      sortOrder: json['sort_order'],
      digitalFiles: json['digital_files'] != null ? List<Map<String, dynamic>>.from(json['digital_files']) : null,
      downloadLimit: json['download_limit'],
      downloadExpiryDays: json['download_expiry_days'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      category: json['category'] != null ? ProductCategory.fromJson(json['category']) : null,
      images: json['images'] != null ? (json['images'] as List).map((i) => ProductImage.fromJson(i)).toList() : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'short_description': shortDescription,
      'type': type,
      'category_id': categoryId,
      'price': price,
      'sale_price': salePrice,
      'sku': sku,
      'stock_quantity': stockQuantity,
      'manage_stock': manageStock,
      'in_stock': inStock,
      'weight': weight,
      'dimensions': dimensions,
      'attributes': attributes,
      'featured_image': featuredImage,
      'gallery_images': galleryImages,
      'is_featured': isFeatured,
      'is_active': isActive,
      'sort_order': sortOrder,
      'digital_files': digitalFiles,
      'download_limit': downloadLimit,
      'download_expiry_days': downloadExpiryDays,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'category': category?.toJson(),
      'images': images?.map((i) => i.toJson()).toList(),
    };
  }

  double get effectivePrice => salePrice ?? price;
  
  bool get isOnSale => salePrice != null && salePrice! < price;
  
  double get discountPercentage {
    if (!isOnSale) return 0;
    return ((price - salePrice!) / price) * 100;
  }
  
  String get formattedPrice => '\$${effectivePrice.toStringAsFixed(2)}';
  
  String get formattedOriginalPrice => '\$${price.toStringAsFixed(2)}';
  
  String? get primaryImage {
    if (featuredImage != null) return featuredImage;
    if (images != null && images!.isNotEmpty) {
      final primaryImg = images!.firstWhere((img) => img.isPrimary, orElse: () => images!.first);
      return primaryImg.imagePath;
    }
    return null;
  }
  
  bool get isDigital => type == 'digital';
  
  bool get isPhysical => type == 'physical';
  
  bool get canPurchase {
    if (!isActive) return false;
    if (isPhysical && manageStock && stockQuantity <= 0) return false;
    return true;
  }
}

class ProductCategory {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? image;
  final String? icon;
  final int? parentId;
  final int sortOrder;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductCategory({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.image,
    this.icon,
    this.parentId,
    required this.sortOrder,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    return ProductCategory(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      image: json['image'],
      icon: json['icon'],
      parentId: json['parent_id'],
      sortOrder: json['sort_order'],
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'image': image,
      'icon': icon,
      'parent_id': parentId,
      'sort_order': sortOrder,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class ProductImage {
  final int id;
  final int productId;
  final String imagePath;
  final String? altText;
  final int sortOrder;
  final bool isPrimary;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductImage({
    required this.id,
    required this.productId,
    required this.imagePath,
    this.altText,
    required this.sortOrder,
    required this.isPrimary,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductImage.fromJson(Map<String, dynamic> json) {
    return ProductImage(
      id: json['id'],
      productId: json['product_id'],
      imagePath: json['image_path'],
      altText: json['alt_text'],
      sortOrder: json['sort_order'],
      isPrimary: json['is_primary'] == 1 || json['is_primary'] == true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'image_path': imagePath,
      'alt_text': altText,
      'sort_order': sortOrder,
      'is_primary': isPrimary,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get fullUrl {
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    return 'https://your-domain.com/storage/$imagePath'; // Replace with your actual domain
  }
}
