<script setup>
import { Link } from '@inertiajs/vue3';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import MedroidLogo from '@/components/MedroidLogo.vue';
import axios from 'axios';
import { useLogout } from '@/composables/useLogout';

const props = defineProps({
  user: {
    type: Object,
    required: false,
    default: () => null
  }
});

const collapsed = ref(false);
const chatHistory = ref([]);
const loadingChats = ref(false);
const creditBalance = ref({
    balance: 0,
    total_earned: 0,
    total_used: 0,
    referral_earnings: 0,
    admin_credits: 0
});

const userRole = computed(() => {
  return props.user?.role || 'patient';
});

const isFounderMember = computed(() => {
  return props.user?.is_founder_member || false;
});

const founderClubInfo = computed(() => {
  return props.user?.founder_club_info || null;
});

// Permission checks based on backend structure
const isAdmin = computed(() => userRole.value === 'admin');
const isProvider = computed(() => userRole.value === 'provider');
const isPatient = computed(() => userRole.value === 'patient');

// Role-based navigation items
const navigationItems = computed(() => {
  const items = [];

  // Admin and Provider dashboard items
  if (isAdmin.value || isProvider.value) {
    items.push({
      title: 'Dashboard',
      href: '/dashboard',
      icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z',
      permission: true
    });
  }

  // Patient-specific items
  if (isPatient.value) {
    items.push(
      {
        title: 'Chat',
        href: '/chat',
        icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
        permission: true
      },
      {
        title: 'Discover',
        href: '/discover',
        icon: 'M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z',
        permission: true
      },
      {
        title: 'Shop',
        href: '/shop',
        icon: 'M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z',
        permission: true
      },
      {
        title: 'Appointments',
        href: '/appointments',
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
        permission: true
      }
    );
  }

  // Provider-specific items
  if (isProvider.value) {
    items.push(
      {
        title: 'Schedule',
        href: '/provider/schedule',
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
        permission: true
      },
      {
        title: 'Availability',
        href: '/provider/availability',
        icon: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
        permission: true
      },
      {
        title: 'Appointments',
        href: '/appointments',
        icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z',
        permission: true
      },
      {
        title: 'My Patients',
        href: '/provider/patients',
        icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z',
        permission: true
      },
      {
        title: 'Services',
        href: '/provider/services',
        icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z',
        permission: true
      },
      {
        title: 'Earnings',
        href: '/provider/earnings',
        icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
        permission: true
      }
    );
  }

  return items;
});

// Management items (Admin only)
const managementItems = computed(() => {
  if (!isAdmin.value) return [];

  return [
    {
      title: 'Users',
      href: '/users',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view users')
    },
    {
      title: 'Providers',
      href: '/providers',
      icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view providers')
    },
    {
      title: 'Patients',
      href: '/patients',
      icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view patients')
    },
    {
      title: 'Clinics',
      href: '/clinics',
      icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view clinics')
    },
    {
      title: 'Appointments',
      href: '/manage/appointments',
      icon: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view appointments')
    },
    {
      title: 'Payments',
      href: '/payments',
      icon: 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view payments')
    },
    {
      title: 'Chats',
      href: '/chats',
      icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view chats')
    },
    {
      title: 'Permissions',
      href: '/permissions',
      icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('manage permissions')
    },
    {
      title: 'Services',
      href: '/services',
      icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('manage services')
    },
    {
      title: 'Email Templates',
      href: '/email-templates',
      icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view email templates')
    },
    {
      title: 'Notifications',
      href: '/notifications',
      icon: 'M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view notifications')
    },
    {
      title: 'Referrals',
      href: '/referrals',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view referrals')
    },
    {
      title: 'Credits',
      href: '/credits',
      icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view credits')
    },
    {
      title: 'Clubs',
      href: '/clubs',
      icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view clubs')
    },
    {
      title: 'Waitlist',
      href: '/waitlist',
      icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      permission: isAdmin.value || props.user?.user_permissions?.includes('view users')
    }
  ].filter(item => item.permission);
});

const canViewUsers = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view users');
});

const canViewProviders = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view providers');
});

const canViewPatients = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view patients');
});

const canViewAnalytics = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view analytics');
});

const canViewAppointments = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view appointments');
});

const canViewPayments = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view payments');
});

const canViewChats = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view chats');
});

const canManageNotifications = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage notifications');
});

const canManageEmailTemplates = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage email templates');
});

const canManagePermissions = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage permissions');
});

const canManageServices = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('manage services');
});

const canViewReferrals = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view referrals');
});

const canViewCredits = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view credits');
});

const canViewClubs = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view clubs');
});

const canViewClinics = computed(() => {
  return isAdmin.value || props.user?.user_permissions?.includes('view clinics');
});

// Group permission checks
const hasAnyManagementPermission = computed(() => {
  return canViewUsers.value || canViewProviders.value || canViewPatients.value || canViewClinics.value ||
         canViewAppointments.value || canViewPayments.value || canViewChats.value || canManageServices.value;
});

const hasAnySystemPermission = computed(() => {
  return canManagePermissions.value || canManageEmailTemplates.value || canManageNotifications.value ||
         canViewReferrals.value || canViewCredits.value || canViewClubs.value;
});

const toggleSidebar = () => {
  collapsed.value = !collapsed.value;
  localStorage.setItem('app-sidebar-collapsed', collapsed.value);
};

const handleResize = () => {
  if (window.innerWidth < 768 && !collapsed.value) {
    collapsed.value = true;
    localStorage.setItem('app-sidebar-collapsed', 'true');
  }
};

// Fetch credit balance for patients
const fetchCreditBalance = async () => {
  if (!isPatient.value) return;

  try {
    const response = await axios.get('/credits-balance');
    creditBalance.value = response.data;
  } catch (error) {
    console.error('Error loading credit balance:', error);
    creditBalance.value = {
      balance: 0,
      total_earned: 0,
      total_used: 0,
      referral_earnings: 0,
      admin_credits: 0
    };
  }
};

// Fetch chat history for patients
const fetchChatHistory = async () => {
  if (!isPatient.value) return;

  loadingChats.value = true;
  try {
    // First, try to update conversation titles for better display
    try {
      await axios.post('/web-api/chat/update-titles', {}, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'X-Requested-With': 'XMLHttpRequest',
        },
        withCredentials: true
      });
    } catch (titleError) {
      console.log('Title update failed (non-critical):', titleError);
    }

    const response = await axios.get('/web-api/chat/history');
    const data = response.data;

    // Handle different response formats
    let conversations = [];
    if (Array.isArray(data)) {
      conversations = data;
    } else if (data.conversations && Array.isArray(data.conversations)) {
      conversations = data.conversations;
    } else if (data.data && Array.isArray(data.data)) {
      conversations = data.data;
    }

    // Safety check: ensure conversations is an array before filtering
    if (!Array.isArray(conversations)) {
      console.warn('conversations is not an array in AppSidebar:', conversations);
      conversations = [];
    }

    // Filter out empty conversations (conversations with no messages or only empty messages)
    const filteredConversations = conversations.filter(conversation => {
      if (!conversation.messages || !Array.isArray(conversation.messages)) {
        return false;
      }

      // Check if conversation has at least one non-empty message
      return conversation.messages.some(message =>
        message && message.content && message.content.trim().length > 0
      );
    });

    // Show only last 8 conversations with messages (increased from 5)
    chatHistory.value = filteredConversations.slice(0, 8);
  } catch (error) {
    console.error('Error fetching chat history:', error);
    chatHistory.value = [];
  } finally {
    loadingChats.value = false;
  }
};

// Format chat title for display
const formatChatTitle = (conversation) => {
  if (conversation.title && conversation.title.trim()) {
    return conversation.title.length > 25
      ? conversation.title.substring(0, 25) + '...'
      : conversation.title;
  }

  // Fallback to first message or default
  if (conversation.messages && conversation.messages.length > 0) {
    const firstMessage = conversation.messages[0].content;
    return firstMessage.length > 25
      ? firstMessage.substring(0, 25) + '...'
      : firstMessage;
  }

  return 'New Chat';
};

// Format relative time
const formatRelativeTime = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

  if (diffInHours < 1) return 'Just now';
  if (diffInHours < 24) return `${diffInHours}h ago`;
  if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
  return date.toLocaleDateString();
};

const { logout } = useLogout();

// Simple logout handler
const handleLogout = () => {
    // Clear cached data
    creditBalance.value = {
        balance: 0,
        total_earned: 0,
        total_used: 0,
        referral_earnings: 0,
        admin_credits: 0
    };
    chatHistory.value = [];

    // Use the logout composable
    logout();
};

onMounted(() => {
  const savedState = localStorage.getItem('app-sidebar-collapsed');
  if (savedState !== null) {
    collapsed.value = savedState === 'true';
  }

  if (window.innerWidth < 768) {
    collapsed.value = true;
  }

  window.addEventListener('resize', handleResize);

  // Fetch chat history and credit balance for patients
  if (isPatient.value) {
    fetchChatHistory();
    fetchCreditBalance();
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="h-screen flex-shrink-0" :class="[collapsed ? 'w-16' : 'w-64', 'transition-all duration-300']">
    <div class="h-full bg-white border-r border-gray-200 text-gray-800 flex flex-col">
      <!-- Header -->
      <div class="flex items-center justify-between p-3 border-b border-gray-200">
        <div class="flex items-center">
          <MedroidLogo :size="28" />
          <span v-if="!collapsed" class="ml-2 text-lg font-semibold text-medroid-navy">Medroid</span>
        </div>
        <button @click="toggleSidebar" class="text-gray-400 hover:text-gray-600 focus:outline-none">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path v-if="collapsed" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      </div>

      <!-- Navigation -->
      <div class="flex-1 overflow-y-auto py-3">
        <nav class="space-y-1 px-2 flex flex-col h-full">
          <!-- Main Navigation Items -->
          <div v-for="item in navigationItems" :key="item.href">
            <Link
              :href="item.href"
              class="flex items-center px-2 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200"
              :class="[
                $page.url === item.href || $page.url.startsWith(item.href + '/')
                  ? 'bg-medroid-orange text-white'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              ]"
            >
              <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
              </svg>
              <span v-if="!collapsed" class="ml-2">{{ item.title }}</span>
            </Link>
          </div>

          <!-- Chat History Section (Patient Only) -->
          <div v-if="isPatient && !collapsed" class="pt-6 flex flex-col flex-1 min-h-0">
            <div class="px-3 mb-2">
              <div class="flex items-center justify-between">
                <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Recent Chats</h3>
                <Link
                  href="/chat"
                  class="text-xs text-medroid-orange hover:text-medroid-orange-dark"
                  title="Start new chat"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </Link>
              </div>
            </div>

            <!-- Chat history container with scrolling -->
            <div class="flex-1 min-h-0 overflow-y-auto">
              <!-- Loading state -->
              <div v-if="loadingChats" class="px-3 py-2">
                <div class="flex items-center text-xs text-gray-500">
                  <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400 mr-2"></div>
                  Loading chats...
                </div>
              </div>

              <!-- Chat history items -->
              <div v-else-if="chatHistory.length > 0" class="space-y-1 px-3">
                <Link
                  v-for="chat in chatHistory"
                  :key="chat._id || chat.id"
                  :href="`/chat?conversation=${chat._id || chat.id}`"
                  class="flex items-start py-2 text-xs rounded-lg transition-colors duration-200 hover:bg-gray-50 group block"
                >
                  <div class="flex-shrink-0 mr-2 mt-0.5">
                    <div class="w-2 h-2 bg-medroid-orange rounded-full"></div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-gray-900 font-medium truncate group-hover:text-medroid-orange">
                      {{ formatChatTitle(chat) }}
                    </p>
                    <p class="text-gray-500 text-xs mt-0.5">
                      {{ formatRelativeTime(chat.updated_at || chat.createdAt) }}
                    </p>
                  </div>
                </Link>
              </div>

              <!-- Empty state -->
              <div v-else class="px-3 py-2">
                <p class="text-xs text-gray-500">No recent chats</p>
                <Link
                  href="/chat"
                  class="text-xs text-medroid-orange hover:text-medroid-orange-dark mt-1 inline-block"
                >
                  Start your first chat
                </Link>
              </div>
            </div>

            <!-- View All Chats Link (only if there are chats) -->
            <div v-if="chatHistory.length > 0" class="px-3 py-2 border-t border-gray-200 mt-auto">
              <!-- View All Chats - Left aligned without background -->
              <Link
                href="/chat-history"
                class="text-xs text-gray-600 hover:text-medroid-orange flex items-center justify-start py-1 transition-colors"
              >
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
                View all chats
              </Link>
            </div>

            <!-- Credit Balance Display - Always visible for patients -->
            <div class="px-3 py-3 border-t border-gray-200" :class="{ 'mt-auto': chatHistory.length === 0 }">
              <Link
                href="/credit-history"
                class="inline-flex items-center justify-between w-auto px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 transition-colors shadow-sm"
                :title="`Total Balance: $${parseFloat(creditBalance.balance || 0).toFixed(2)} | Referrals: $${parseFloat(creditBalance.referral_earnings || 0).toFixed(2)} | Admin: $${parseFloat(creditBalance.admin_credits || 0).toFixed(2)} | Used: $${parseFloat(creditBalance.total_used || 0).toFixed(2)}`"
              >
                <div class="flex items-center">
                  <svg class="w-3 h-3 mr-1 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-xs text-gray-600 hover:text-medroid-orange mr-2">Credits:</span>
                </div>
                <span class="text-xs font-semibold text-green-600 hover:text-medroid-orange">${{ parseFloat(creditBalance.balance || 0).toFixed(2) }}</span>
              </Link>
            </div>
          </div>

          <!-- Management Section (Admin Only) -->
          <div v-if="managementItems.length > 0" class="pt-6">
            <div v-if="!collapsed" class="px-3 mb-2">
              <h3 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Management</h3>
            </div>
            <div v-for="item in managementItems" :key="item.href">
              <Link
                :href="item.href"
                class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
                :class="[
                  $page.url === item.href || $page.url.startsWith(item.href + '/')
                    ? 'bg-medroid-orange text-white'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                ]"
              >
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                </svg>
                <span v-if="!collapsed" class="ml-3">{{ item.title }}</span>
              </Link>
            </div>
          </div>
        </nav>
      </div>

      <!-- User Section -->
      <div class="border-t border-gray-200 p-3">
        <div class="flex items-center">
          <div class="w-7 h-7 bg-gray-300 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div v-if="!collapsed" class="ml-2 flex-1">
            <div class="flex items-center gap-2">
              <p class="text-sm font-medium text-gray-900">{{ user?.name }}</p>
              <!-- Founders Badge -->
              <div v-if="isFounderMember"
                   class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-sm"
                   :title="founderClubInfo?.club_name || 'Medroid Founders Club'">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                Founders' Club
              </div>
            </div>
            <p class="text-xs text-gray-500 capitalize">{{ userRole }}</p>
          </div>
        </div>

        <!-- Settings and Logout -->
        <div v-if="!collapsed" class="mt-3 space-y-1">
          <Link
            href="/settings/profile"
            class="flex items-center px-2 py-1 text-xs text-gray-600 hover:text-gray-900 rounded"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Settings
          </Link>
          <button
            @click="handleLogout"
            class="flex items-center px-2 py-1 text-xs text-red-600 hover:text-red-800 rounded w-full text-left"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Logout
          </button>
        </div>

        <!-- Collapsed state logout button -->
        <div v-else class="mt-3">
          <button
            @click="handleLogout"
            class="flex items-center justify-center p-2 text-red-600 hover:text-red-800 rounded"
            title="Logout"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>


    </div>
  </div>
</template>

<style scoped>
/* All styling is handled by Tailwind CSS classes */
</style>
